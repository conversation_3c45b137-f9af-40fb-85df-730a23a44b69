package com.styl.keycloak.identity.provider.isams;

import org.keycloak.broker.oidc.OIDCIdentityProviderConfig;
import org.keycloak.models.IdentityProviderModel;

public class ISamsIdentityProviderConfig extends OIDCIdentityProviderConfig {

    public ISamsIdentityProviderConfig(IdentityProviderModel model) {
        super(model);
    }

    public ISamsIdentityProviderConfig() {

    }

    public String getResponseType() {
        String responseType = getConfig().get("responseType");
        return responseType == null || responseType.isEmpty() ? ISamsIdentityProvider.DEFAULT_RESPONSE_TYPE : responseType;
    }

    @Override
    public boolean isDisableNonce() {
        return true;
    }

}
