package com.styl.keycloak.identity.provider.uwcsea;

import org.keycloak.broker.provider.AbstractIdentityProviderFactory;
import org.keycloak.broker.social.SocialIdentityProviderFactory;
import org.keycloak.models.IdentityProviderModel;
import org.keycloak.models.KeycloakSession;
import org.keycloak.provider.ProviderConfigProperty;
import org.keycloak.provider.ProviderConfigurationBuilder;

import java.util.List;

public class UWCSEAIdentityProviderFactory extends AbstractIdentityProviderFactory<UWCSEAIdentityProvider> implements SocialIdentityProviderFactory<UWCSEAIdentityProvider> {

    public static final String PROVIDER_ID = "uwcsea";

    @Override
    public String getName() {
        return "UWCSEA";
    }

    @Override
    public UWCSEAIdentityProvider create(KeycloakSession session, IdentityProviderModel model) {
        return new UWCSEAIdentityProvider(session, new UWCSEAIdentityProviderConfig(model));
    }

    @Override
    public UWCSEAIdentityProviderConfig createConfig() {
        return new UWCSEAIdentityProviderConfig();
    }

    @Override
    public String getId() {
        return PROVIDER_ID;
    }

    @Override
    public List<ProviderConfigProperty> getConfigProperties() {
        return ProviderConfigurationBuilder.create()
                .property().name("authorizationUrl")
                .label("Authorization URL")
                .type(ProviderConfigProperty.STRING_TYPE)
                .required(true)
                .add()
                .property().name("secretKey")
                .label("Secret Key")
                .helpText("The secret key used to verify the hashed_token")
                .type(ProviderConfigProperty.PASSWORD)
                .required(true)
                .add().build();
    }
}
