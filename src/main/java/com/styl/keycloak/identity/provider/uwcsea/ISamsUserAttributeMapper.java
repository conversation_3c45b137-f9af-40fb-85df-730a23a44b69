package com.styl.keycloak.identity.provider.isams;

import org.keycloak.broker.oidc.mappers.AbstractJsonUserAttributeMapper;

public class ISamsUserAttributeMapper extends AbstractJsonUserAttributeMapper {

    private static final String[] cp = new String[] { ISamsIdentityProviderFactory.PROVIDER_ID };

    @Override
    public String[] getCompatibleProviders() {
        return cp;
    }

    @Override
    public String getId() {
        return "isams-user-attribute-mapper";
    }

}
