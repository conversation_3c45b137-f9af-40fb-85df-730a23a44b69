package com.styl.keycloak.identity.provider.isams;

import org.keycloak.broker.provider.AbstractIdentityProviderFactory;
import org.keycloak.broker.social.SocialIdentityProviderFactory;
import org.keycloak.models.IdentityProviderModel;
import org.keycloak.models.KeycloakSession;
import org.keycloak.provider.ProviderConfigProperty;
import org.keycloak.provider.ProviderConfigurationBuilder;

import java.util.List;

public class ISamsIdentityProviderFactory extends AbstractIdentityProviderFactory<ISamsIdentityProvider> implements SocialIdentityProviderFactory<ISamsIdentityProvider> {

    public static final String PROVIDER_ID = "isams";

    @Override
    public String getName() {
        return "ISAMS";
    }

    @Override
    public ISamsIdentityProvider create(KeycloakSession session, IdentityProviderModel model) {
        return new ISamsIdentityProvider(session, new ISamsIdentityProviderConfig(model));
    }

    @Override
    public ISamsIdentityProviderConfig createConfig() {
        return new ISamsIdentityProviderConfig();
    }

    @Override
    public String getId() {
        return PROVIDER_ID;
    }

    @Override
    public List<ProviderConfigProperty> getConfigProperties() {
        return ProviderConfigurationBuilder.create()
                .property().name("authorizationUrl")
                .label("Authorization URL")
                .type(ProviderConfigProperty.STRING_TYPE)
                .required(true)
                .add()
                .property().name("tokenUrl")
                .label("Token URL")
                .type(ProviderConfigProperty.STRING_TYPE)
                .required(true)
                .add()
                .property().name("userInfoUrl")
                .label("User Info URL")
                .type(ProviderConfigProperty.STRING_TYPE)
                .required(true)
                .add()
                .property().name("responseType")
                .label("Response Type")
                .defaultValue("code token")
                .helpText("Set 'response_type' query parameter when logging in with ISAMS. The allowed values are either " +
                        "'code token' or 'code id_token' or 'code id_token token'.")
                .type(ProviderConfigProperty.STRING_TYPE)
                .add().build();
    }
}
