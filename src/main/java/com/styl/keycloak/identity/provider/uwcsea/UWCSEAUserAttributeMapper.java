package com.styl.keycloak.identity.provider.uwcsea;

import org.keycloak.broker.oidc.mappers.AbstractJsonUserAttributeMapper;

public class UWCSEAUserAttributeMapper extends AbstractJsonUserAttributeMapper {

    private static final String[] cp = new String[] { UWCSEAIdentityProviderFactory.PROVIDER_ID };

    @Override
    public String[] getCompatibleProviders() {
        return cp;
    }

    @Override
    public String getId() {
        return "isams-user-attribute-mapper";
    }

}
