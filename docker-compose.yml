version: '3.8'

services:
  postgres:
    image: postgres:16-alpine3.20
    container_name: postgres-db
    environment:
      POSTGRES_DB: keycloak_db
      POSTGRES_USER: keycloak_user
      POSTGRES_PASSWORD: keycloak_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - keycloak-network

  keycloak:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        KEYCLOAK_VERSION: "26.3.4"
    container_name: keycloak
    environment:
      DB_VENDOR: postgres
      DB_ADDR: postgres
      DB_DATABASE: keycloak_db
      DB_USER: keycloak_user
      DB_PASSWORD: keycloak_password
      KC_BOOTSTRAP_ADMIN_USERNAME: admin
      KC_BOOTSTRAP_ADMIN_PASSWORD: admin
    ports:
      - "8200:8080"
    depends_on:
      - postgres
    networks:
      - keycloak-network
    command: start-dev --verbose --log-level=DEBUG,com.styl:debug

volumes:
  postgres_data:

networks:
  keycloak-network:
    driver: bridge
