<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting...</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        h1 {
            margin: 0 0 1rem 0;
            font-size: 2rem;
        }
        .spinner {
            margin: 2rem auto;
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-top: 5px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        #countdown {
            font-size: 3rem;
            font-weight: bold;
            margin: 1rem 0;
        }
        p {
            font-size: 1rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Authentication Successful</h1>
        <div class="spinner"></div>
        <p>Redirecting in</p>
        <div id="countdown">5</div>
        <p>seconds...</p>
    </div>

    <!-- Hidden form that will auto-submit -->
    <form id="authForm" method="POST" action="{{REDIRECT_URL}}" style="display: none;">
        <input type="hidden" name="state" value="{{STATE}}">
    </form>

    <script>
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        const form = document.getElementById('authForm');

        // Update countdown every second
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                form.submit();
            }
        }, 1000);
    </script>
</body>
</html>
