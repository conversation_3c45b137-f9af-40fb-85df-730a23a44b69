package com.styl.keycloak.identity.provider.uwcsea;

import org.keycloak.broker.oidc.OAuth2IdentityProviderConfig;
import org.keycloak.broker.oidc.OIDCIdentityProviderConfig;
import org.keycloak.models.IdentityProviderModel;

public class UWCSEAIdentityProviderConfig extends OAuth2IdentityProviderConfig {

    public UWCSEAIdentityProviderConfig(IdentityProviderModel model) {
        super(model);
    }

    public UWCSEAIdentityProviderConfig() {

    }

    public String getResponseType() {
        String responseType = getConfig().get("responseType");
        return responseType == null || responseType.isEmpty() ? UWCSEAIdentityProvider.DEFAULT_RESPONSE_TYPE : responseType;
    }

    @Override
    public boolean isDisableNonce() {
        return true;
    }

}
