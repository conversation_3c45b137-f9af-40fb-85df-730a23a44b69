package com.styl.keycloak.identity.provider.isams;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriBuilder;
import org.apache.commons.lang3.StringUtils;
import org.keycloak.OAuth2Constants;
import org.keycloak.broker.oidc.OIDCIdentityProvider;
import org.keycloak.broker.oidc.OIDCIdentityProviderConfig;
import org.keycloak.broker.oidc.mappers.AbstractJsonUserAttributeMapper;
import org.keycloak.broker.provider.AuthenticationRequest;
import org.keycloak.broker.provider.BrokeredIdentityContext;
import org.keycloak.broker.provider.IdentityBrokerException;
import org.keycloak.broker.provider.util.IdentityBrokerState;
import org.keycloak.broker.provider.util.SimpleHttp;
import org.keycloak.broker.social.SocialIdentityProvider;
import org.keycloak.common.ClientConnection;
import org.keycloak.common.util.KeycloakUriBuilder;
import org.keycloak.events.Errors;
import org.keycloak.events.EventBuilder;
import org.keycloak.events.EventType;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.RealmModel;
import org.keycloak.services.ErrorPage;
import org.keycloak.services.messages.Messages;
import org.keycloak.sessions.AuthenticationSessionModel;
import org.keycloak.social.google.GoogleIdentityProviderConfig;

import java.util.Optional;

public class ISamsIdentityProvider extends OIDCIdentityProvider implements SocialIdentityProvider<OIDCIdentityProviderConfig> {

    public static final String DEFAULT_SCOPE = "openid profile email";
    public static final String DEFAULT_RESPONSE_TYPE = "code token";

    public static final String OIDC_PARAMETER_RESPONSE_MODE = "response_mode";
    public static final String DEFAULT_RESPONSE_MODE = "form_post";

    public ISamsIdentityProvider(KeycloakSession session, ISamsIdentityProviderConfig config) {
        super(session, config);
    }

    @Override
    protected String getDefaultScopes() {
        return DEFAULT_SCOPE;
    }

    @Override
    protected UriBuilder createAuthorizationUrl(AuthenticationRequest request) {
        UriBuilder uriBuilder = super.createAuthorizationUrl(request);
        final ISamsIdentityProviderConfig googleConfig = (ISamsIdentityProviderConfig) getConfig();
        String responseType = googleConfig.getResponseType();

        uriBuilder.queryParam(OIDC_PARAMETER_RESPONSE_MODE, DEFAULT_RESPONSE_MODE);
        if (responseType != null) {
            uriBuilder.replaceQueryParam(OAuth2Constants.RESPONSE_TYPE, responseType);
        }

        return uriBuilder;
    }

    @Override
    public Object callback(RealmModel realm, AuthenticationCallback callback, EventBuilder event) {
        return new OIDCEndpoint(session, callback, event, this);
    }

    protected static class OIDCEndpoint {
        protected final RealmModel realm;
        protected final AuthenticationCallback callback;
        protected final EventBuilder event;
        private final ISamsIdentityProvider provider;

        protected final KeycloakSession session;

        protected final ClientConnection clientConnection;

        public OIDCEndpoint(KeycloakSession session, AuthenticationCallback callback, EventBuilder event, ISamsIdentityProvider provider) {
            this.session = session;
            this.realm = session.getContext().getRealm();
            this.clientConnection = session.getContext().getConnection();
            this.callback = callback;
            this.event = event;
            this.provider = provider;
        }

        @GET
        @Path("")
        public Response testAuthResponse(@QueryParam("access_token") String accessToken, @QueryParam("state") String state) {
            return authResponse(accessToken, state);
        }

        @POST
        @Path("")
        public Response authResponse(@FormParam("access_token") String accessToken, @FormParam("state") String state) {
            if (state == null) {
                return errorIdentityProviderLogin(Messages.IDENTITY_PROVIDER_MISSING_STATE_ERROR);
            }

            IdentityBrokerState idpState = IdentityBrokerState.encoded(state, realm);
            String clientId = idpState.getClientId();
            String tabId = idpState.getTabId();
            if (clientId == null || tabId == null) {
                logger.errorf("Invalid state parameter: %s", state);
                return errorIdentityProviderLogin(Messages.INVALID_REQUEST, Response.Status.BAD_REQUEST);
            }

            AuthenticationSessionModel authSession = this.callback.getAndVerifyAuthenticationSession(state);
            session.getContext().setAuthenticationSession(authSession);
            try {
                if (StringUtils.isNotBlank(accessToken)) {
                    BrokeredIdentityContext identity = authenticationContext(accessToken);
                    logger.infof("User Logged in via ISAMS: %s", identity.getEmail());
                    identity.setIdp(provider);
                    identity.setAuthenticationSession(authSession);
                    return callback.authenticated(identity);
                }
            } catch (WebApplicationException e) {
                return e.getResponse();
            } catch (Exception e) {
                logger.error("Failed to complete ISAMS identity provider oauth callback", e);
            }
            return errorIdentityProviderLogin(Messages.IDENTITY_PROVIDER_UNEXPECTED_ERROR);
        }

        private BrokeredIdentityContext authenticationContext(String accessToken) {
            try {
                JsonNode profile = SimpleHttp.doGet(provider.getUserInfoUrl(), session).auth(accessToken).asJson();
                return getFederatedIdentity(profile);
            } catch (Exception e) {
                throw new IdentityBrokerException("Could not obtain user profile from ISAMS", e);
            }
        }

        public BrokeredIdentityContext getFederatedIdentity(JsonNode response) throws JsonProcessingException {
            ISamsUserPresentation isamsUser = parseUser(response);
            BrokeredIdentityContext user = new BrokeredIdentityContext(isamsUser.getSub(), provider.getConfig());

            user.setBrokerUserId(isamsUser.getSub());
            user.setUsername(isamsUser.getEmail());
            user.setFirstName(isamsUser.getName());
            user.setLastName(isamsUser.getFamilyName());
            user.setEmail(isamsUser.getEmail());
            AbstractJsonUserAttributeMapper.storeUserProfileForMapper(user, isamsUser.getProfile(), provider.getConfig().getAlias());
            return user;
        }

        private ISamsUserPresentation parseUser(JsonNode profile) throws JsonProcessingException {
            ISamsUserPresentation isamsUser = new ISamsUserPresentation();
            isamsUser.setProfile(profile);
            isamsUser.setEmail(Optional.ofNullable(profile.get("email")).map(JsonNode::asText).orElse(null));
            isamsUser.setEmailVerified(Optional.ofNullable(profile.get("email_verified")).map(JsonNode::asBoolean).orElse(false));
            isamsUser.setFamilyName(Optional.ofNullable(profile.get("family_name")).map(JsonNode::asText).orElse(null));
            isamsUser.setName(Optional.ofNullable(profile.get("name")).map(JsonNode::asText).orElse(null));
            isamsUser.setNickname(Optional.ofNullable(profile.get("nickname")).map(JsonNode::asText).orElse(null));
            isamsUser.setSub(Optional.ofNullable(profile.get("sub")).map(JsonNode::asText).orElse(null));

            return isamsUser;
        }

        private Response errorIdentityProviderLogin(String message) {
            return errorIdentityProviderLogin(message, Response.Status.BAD_GATEWAY);
        }

        private Response errorIdentityProviderLogin(String message, Response.Status status) {
            sendErrorEvent();
            return ErrorPage.error(session, null, status, message);
        }

        private void sendErrorEvent() {
            event.event(EventType.IDENTITY_PROVIDER_LOGIN);
            event.detail("idp", provider.getConfig().getProviderId());
            event.error(Errors.IDENTITY_PROVIDER_LOGIN_FAILURE);
        }
    }

    @Override
    public void authenticationFinished(AuthenticationSessionModel authSession, BrokeredIdentityContext context) {
        return;
    }
}
