const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 8080;

// Create the server
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  
  console.log(`Received request: ${req.method} ${req.url}`);
  console.log(`Parsed URL: ${JSON.stringify(parsedUrl)}`);

  // Handle /auth route
  if (pathname === '/auth') {
    const { state, redirect_uri } = parsedUrl.query;
    
    console.log(`Received state: ${state}`);
    console.log(`Received redirectUrl: ${redirect_uri}`);

    if (!state || !redirect_uri) {
      res.writeHead(400, { 'Content-Type': 'text/plain' });
      res.end('Missing required parameters: state and redirect_uri');
      return;
    }
    
    // Read the HTML template
    const filePath = path.join(__dirname, 'index.html');
    
    fs.readFile(filePath, 'utf8', (err, data) => {
      if (err) {
        console.error(err);
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Error loading HTML file');
        return;
      }
      
      // Replace placeholders with actual values
      const html = data
        .replace('{{STATE}}', state)
        .replace('{{REDIRECT_URL}}', redirect_uri);
      
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(html);
    });
  } else {
    // Default route
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found - Please use /auth?state=YOUR_STATE&redirect_uri=YOUR_URL');
  }
});

// Start the server
server.listen(PORT, () => {
  console.log(`Server is running at http://localhost:${PORT}`);
  console.log(`Example: http://localhost:${PORT}/auth?state=abc123&redirect_uri=https://example.com/callback`);
});
